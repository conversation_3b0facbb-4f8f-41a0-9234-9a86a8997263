{"cells": [{"cell_type": "markdown", "id": "c755214a", "metadata": {}, "source": ["# Title: Personalized Medical Recommendation System with Machine Learning\n", "\n", "# Description:\n", "\n", "Welcome to our cutting-edge Personalized Medical Recommendation System, a powerful platform designed to assist users in understanding and managing their health. Leveraging the capabilities of machine learning, our system analyzes user-input symptoms to predict potential diseases accurately."]}, {"cell_type": "markdown", "id": "db119e1e", "metadata": {}, "source": ["# load dataset & tools"]}, {"cell_type": "code", "execution_count": 1, "id": "4e4766bf", "metadata": {}, "outputs": [], "source": ["import  pandas as pd"]}, {"cell_type": "code", "execution_count": 2, "id": "56ce4778", "metadata": {}, "outputs": [], "source": ["dataset = pd.read_csv('Training.csv')"]}, {"cell_type": "code", "execution_count": 3, "id": "5f18d6d2", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>itching</th>\n", "      <th>skin_rash</th>\n", "      <th>nodal_skin_eruptions</th>\n", "      <th>continuous_sneezing</th>\n", "      <th>shivering</th>\n", "      <th>chills</th>\n", "      <th>joint_pain</th>\n", "      <th>stomach_pain</th>\n", "      <th>acidity</th>\n", "      <th>ulcers_on_tongue</th>\n", "      <th>...</th>\n", "      <th>blackheads</th>\n", "      <th>scurring</th>\n", "      <th>skin_peeling</th>\n", "      <th>silver_like_dusting</th>\n", "      <th>small_dents_in_nails</th>\n", "      <th>inflammatory_nails</th>\n", "      <th>blister</th>\n", "      <th>red_sore_around_nose</th>\n", "      <th>yellow_crust_ooze</th>\n", "      <th>prognosis</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Fungal infection</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Fungal infection</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Fungal infection</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Fungal infection</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Fungal infection</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4915</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>(vertigo) Paroymsal  Positional Vertigo</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4916</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Acne</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4917</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Urinary tract infection</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4918</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Psoriasis</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4919</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Impetigo</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4920 rows × 133 columns</p>\n", "</div>"], "text/plain": ["      itching  skin_rash  nodal_skin_eruptions  continuous_sneezing  \\\n", "0           1          1                     1                    0   \n", "1           0          1                     1                    0   \n", "2           1          0                     1                    0   \n", "3           1          1                     0                    0   \n", "4           1          1                     1                    0   \n", "...       ...        ...                   ...                  ...   \n", "4915        0          0                     0                    0   \n", "4916        0          1                     0                    0   \n", "4917        0          0                     0                    0   \n", "4918        0          1                     0                    0   \n", "4919        0          1                     0                    0   \n", "\n", "      shivering  chills  joint_pain  stomach_pain  acidity  ulcers_on_tongue  \\\n", "0             0       0           0             0        0                 0   \n", "1             0       0           0             0        0                 0   \n", "2             0       0           0             0        0                 0   \n", "3             0       0           0             0        0                 0   \n", "4             0       0           0             0        0                 0   \n", "...         ...     ...         ...           ...      ...               ...   \n", "4915          0       0           0             0        0                 0   \n", "4916          0       0           0             0        0                 0   \n", "4917          0       0           0             0        0                 0   \n", "4918          0       0           1             0        0                 0   \n", "4919          0       0           0             0        0                 0   \n", "\n", "      ...  blackheads  scurring  skin_peeling  silver_like_dusting  \\\n", "0     ...           0         0             0                    0   \n", "1     ...           0         0             0                    0   \n", "2     ...           0         0             0                    0   \n", "3     ...           0         0             0                    0   \n", "4     ...           0         0             0                    0   \n", "...   ...         ...       ...           ...                  ...   \n", "4915  ...           0         0             0                    0   \n", "4916  ...           1         1             0                    0   \n", "4917  ...           0         0             0                    0   \n", "4918  ...           0         0             1                    1   \n", "4919  ...           0         0             0                    0   \n", "\n", "      small_dents_in_nails  inflammatory_nails  blister  red_sore_around_nose  \\\n", "0                        0                   0        0                     0   \n", "1                        0                   0        0                     0   \n", "2                        0                   0        0                     0   \n", "3                        0                   0        0                     0   \n", "4                        0                   0        0                     0   \n", "...                    ...                 ...      ...                   ...   \n", "4915                     0                   0        0                     0   \n", "4916                     0                   0        0                     0   \n", "4917                     0                   0        0                     0   \n", "4918                     1                   1        0                     0   \n", "4919                     0                   0        1                     1   \n", "\n", "      yellow_crust_ooze                                prognosis  \n", "0                     0                         Fungal infection  \n", "1                     0                         Fungal infection  \n", "2                     0                         Fungal infection  \n", "3                     0                         Fungal infection  \n", "4                     0                         Fungal infection  \n", "...                 ...                                      ...  \n", "4915                  0  (vertigo) Paroymsal  Positional Vertigo  \n", "4916                  0                                     Acne  \n", "4917                  0                  Urinary tract infection  \n", "4918                  0                                Psorias<PERSON>  \n", "4919                  1                                 Impetigo  \n", "\n", "[4920 rows x 133 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset"]}, {"cell_type": "code", "execution_count": 4, "id": "f49b2b12", "metadata": {}, "outputs": [], "source": ["# vals = dataset.values.flatten()"]}, {"cell_type": "code", "execution_count": 5, "id": "a49049bd", "metadata": {}, "outputs": [{"data": {"text/plain": ["(4920, 133)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset.shape"]}, {"cell_type": "markdown", "id": "2db916ab", "metadata": {}, "source": ["# train test split"]}, {"cell_type": "code", "execution_count": 15, "id": "b1e9c647", "metadata": {}, "outputs": [], "source": ["from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import LabelEncoder"]}, {"cell_type": "code", "execution_count": 16, "id": "4cb2e972", "metadata": {}, "outputs": [], "source": ["X = dataset.drop('prognosis', axis=1)\n", "y = dataset['prognosis']\n", "\n", "# ecoding prognonsis\n", "le = LabelEncoder()\n", "le.fit(y)\n", "Y = le.transform(y)\n", "    \n", "X_train, X_test, y_train, y_test = train_test_split(X, Y, test_size=0.3, random_state=20)"]}, {"cell_type": "markdown", "id": "1c1a9ed2", "metadata": {}, "source": ["# Training top models"]}, {"cell_type": "code", "execution_count": 8, "id": "5b9c4a9e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SVC Accuracy: 1.0\n", "SVC Confusion Matrix:\n", "[[40,  0,  0, ...,  0,  0,  0],\n", " [ 0, 43,  0, ...,  0,  0,  0],\n", " [ 0,  0, 28, ...,  0,  0,  0],\n", " ...,\n", " [ 0,  0,  0, ..., 34,  0,  0],\n", " [ 0,  0,  0, ...,  0, 41,  0],\n", " [ 0,  0,  0, ...,  0,  0, 31]]\n", "\n", "========================================\n", "\n", "RandomForest Accuracy: 1.0\n", "RandomForest Confusion Matrix:\n", "[[40,  0,  0, ...,  0,  0,  0],\n", " [ 0, 43,  0, ...,  0,  0,  0],\n", " [ 0,  0, 28, ...,  0,  0,  0],\n", " ...,\n", " [ 0,  0,  0, ..., 34,  0,  0],\n", " [ 0,  0,  0, ...,  0, 41,  0],\n", " [ 0,  0,  0, ...,  0,  0, 31]]\n", "\n", "========================================\n", "\n", "GradientBoosting Accuracy: 1.0\n", "GradientBoosting Confusion Matrix:\n", "[[40,  0,  0, ...,  0,  0,  0],\n", " [ 0, 43,  0, ...,  0,  0,  0],\n", " [ 0,  0, 28, ...,  0,  0,  0],\n", " ...,\n", " [ 0,  0,  0, ..., 34,  0,  0],\n", " [ 0,  0,  0, ...,  0, 41,  0],\n", " [ 0,  0,  0, ...,  0,  0, 31]]\n", "\n", "========================================\n", "\n", "KNeighbors Accuracy: 1.0\n", "KNeighbors Confusion Matrix:\n", "[[40,  0,  0, ...,  0,  0,  0],\n", " [ 0, 43,  0, ...,  0,  0,  0],\n", " [ 0,  0, 28, ...,  0,  0,  0],\n", " ...,\n", " [ 0,  0,  0, ..., 34,  0,  0],\n", " [ 0,  0,  0, ...,  0, 41,  0],\n", " [ 0,  0,  0, ...,  0,  0, 31]]\n", "\n", "========================================\n", "\n", "MultinomialNB Accuracy: 1.0\n", "MultinomialNB Confusion Matrix:\n", "[[40,  0,  0, ...,  0,  0,  0],\n", " [ 0, 43,  0, ...,  0,  0,  0],\n", " [ 0,  0, 28, ...,  0,  0,  0],\n", " ...,\n", " [ 0,  0,  0, ..., 34,  0,  0],\n", " [ 0,  0,  0, ...,  0, 41,  0],\n", " [ 0,  0,  0, ...,  0,  0, 31]]\n", "\n", "========================================\n", "\n"]}], "source": ["from sklearn.datasets import make_classification\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.svm import SVC\n", "from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier\n", "from sklearn.neighbors import KNeighborsClassifier\n", "from sklearn.naive_bayes import MultinomialNB\n", "from sklearn.metrics import accuracy_score, confusion_matrix\n", "import numpy as np\n", "\n", "\n", "# Create a dictionary to store models\n", "models = {\n", "    'SVC': SVC(kernel='linear'),\n", "    'RandomForest': RandomForestClassifier(n_estimators=100, random_state=42),\n", "    'GradientBoosting': GradientBoostingClassifier(n_estimators=100, random_state=42),\n", "    'KNeighbors': KNeighborsClassifier(n_neighbors=5),\n", "    'MultinomialNB': MultinomialNB()\n", "}\n", "\n", "# Loop through the models, train, test, and print results\n", "for model_name, model in models.items():\n", "    # Train the model\n", "    model.fit(X_train, y_train)\n", "\n", "    # Test the model\n", "    predictions = model.predict(X_test)\n", "\n", "    # Calculate accuracy\n", "    accuracy = accuracy_score(y_test, predictions)\n", "    print(f\"{model_name} Accuracy: {accuracy}\")\n", "\n", "    # Calculate confusion matrix\n", "    cm = confusion_matrix(y_test, predictions)\n", "    print(f\"{model_name} Confusion Matrix:\")\n", "    print(np.array2string(cm, separator=', '))\n", "\n", "    print(\"\\n\" + \"=\"*40 + \"\\n\")\n"]}, {"cell_type": "markdown", "id": "36cee3c8", "metadata": {}, "source": ["# single prediction"]}, {"cell_type": "code", "execution_count": 18, "id": "a74ad639", "metadata": {}, "outputs": [{"data": {"text/plain": ["1.0"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["# selecting svc\n", "svc = SVC(kernel='linear')\n", "svc.fit(X_train,y_train)\n", "ypred = svc.predict(X_test)\n", "accuracy_score(y_test,ypred)"]}, {"cell_type": "code", "execution_count": 114, "id": "fdd98daa", "metadata": {}, "outputs": [], "source": ["# save svc\n", "import pickle\n", "pickle.dump(svc,open('svc.pkl','wb'))"]}, {"cell_type": "code", "execution_count": 115, "id": "4dd13145", "metadata": {}, "outputs": [], "source": ["# load model\n", "svc = pickle.load(open('svc.pkl','rb'))"]}, {"cell_type": "code", "execution_count": 116, "id": "8bf40f9d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["predicted disease : [40]\n", "Actual Disease : 40\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\sklearn\\base.py:465: UserWarning: X does not have valid feature names, but SVC was fitted with feature names\n", "  warnings.warn(\n"]}], "source": ["# test 1:\n", "print(\"predicted disease :\",svc.predict(X_test.iloc[0].values.reshape(1,-1)))\n", "print(\"Actual Disease :\", y_test[0])"]}, {"cell_type": "code", "execution_count": 117, "id": "786bfd1a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["predicted disease : [39]\n", "Actual Disease : 39\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\sklearn\\base.py:465: UserWarning: X does not have valid feature names, but SVC was fitted with feature names\n", "  warnings.warn(\n"]}], "source": ["# test 2:\n", "print(\"predicted disease :\",svc.predict(X_test.iloc[100].values.reshape(1,-1)))\n", "print(\"Actual Disease :\", y_test[100])"]}, {"cell_type": "markdown", "id": "9ce6884a", "metadata": {}, "source": ["# Recommendation System and Prediction"]}, {"cell_type": "markdown", "id": "f53f59b8", "metadata": {}, "source": ["# load database and use logic for recommendations"]}, {"cell_type": "code", "execution_count": 118, "id": "767ed813", "metadata": {}, "outputs": [], "source": ["sym_des = pd.read_csv(\"symtoms_df.csv\")\n", "precautions = pd.read_csv(\"precautions_df.csv\")\n", "workout = pd.read_csv(\"workout_df.csv\")\n", "description = pd.read_csv(\"description.csv\")\n", "medications = pd.read_csv('medications.csv')\n", "diets = pd.read_csv(\"diets.csv\")"]}, {"cell_type": "code", "execution_count": 119, "id": "6cb123a9", "metadata": {}, "outputs": [], "source": ["#============================================================\n", "# custome and helping functions\n", "#==========================helper funtions================\n", "def helper(dis):\n", "    desc = description[description['Disease'] == predicted_disease]['Description']\n", "    desc = \" \".join([w for w in desc])\n", "\n", "    pre = precautions[precautions['Disease'] == dis][['Precaution_1', 'Precaution_2', 'Precaution_3', 'Precaution_4']]\n", "    pre = [col for col in pre.values]\n", "\n", "    med = medications[medications['Disease'] == dis]['Medication']\n", "    med = [med for med in med.values]\n", "\n", "    die = diets[diets['Disease'] == dis]['Diet']\n", "    die = [die for die in die.values]\n", "\n", "    wrkout = workout[workout['disease'] == dis] ['workout']\n", "\n", "\n", "    return desc,pre,med,die,wrkout\n", "\n", "symptoms_dict = {'itching': 0, 'skin_rash': 1, 'nodal_skin_eruptions': 2, 'continuous_sneezing': 3, 'shivering': 4, 'chills': 5, 'joint_pain': 6, 'stomach_pain': 7, 'acidity': 8, 'ulcers_on_tongue': 9, 'muscle_wasting': 10, 'vomiting': 11, 'burning_micturition': 12, 'spotting_ urination': 13, 'fatigue': 14, 'weight_gain': 15, 'anxiety': 16, 'cold_hands_and_feets': 17, 'mood_swings': 18, 'weight_loss': 19, 'restlessness': 20, 'lethargy': 21, 'patches_in_throat': 22, 'irregular_sugar_level': 23, 'cough': 24, 'high_fever': 25, 'sunken_eyes': 26, 'breathlessness': 27, 'sweating': 28, 'dehydration': 29, 'indigestion': 30, 'headache': 31, 'yellowish_skin': 32, 'dark_urine': 33, 'nausea': 34, 'loss_of_appetite': 35, 'pain_behind_the_eyes': 36, 'back_pain': 37, 'constipation': 38, 'abdominal_pain': 39, 'diarrhoea': 40, 'mild_fever': 41, 'yellow_urine': 42, 'yellowing_of_eyes': 43, 'acute_liver_failure': 44, 'fluid_overload': 45, 'swelling_of_stomach': 46, 'swelled_lymph_nodes': 47, 'malaise': 48, 'blurred_and_distorted_vision': 49, 'phlegm': 50, 'throat_irritation': 51, 'redness_of_eyes': 52, 'sinus_pressure': 53, 'runny_nose': 54, 'congestion': 55, 'chest_pain': 56, 'weakness_in_limbs': 57, 'fast_heart_rate': 58, 'pain_during_bowel_movements': 59, 'pain_in_anal_region': 60, 'bloody_stool': 61, 'irritation_in_anus': 62, 'neck_pain': 63, 'dizziness': 64, 'cramps': 65, 'bruising': 66, 'obesity': 67, 'swollen_legs': 68, 'swollen_blood_vessels': 69, 'puffy_face_and_eyes': 70, 'enlarged_thyroid': 71, 'brittle_nails': 72, 'swollen_extremeties': 73, 'excessive_hunger': 74, 'extra_marital_contacts': 75, 'drying_and_tingling_lips': 76, 'slurred_speech': 77, 'knee_pain': 78, 'hip_joint_pain': 79, 'muscle_weakness': 80, 'stiff_neck': 81, 'swelling_joints': 82, 'movement_stiffness': 83, 'spinning_movements': 84, 'loss_of_balance': 85, 'unsteadiness': 86, 'weakness_of_one_body_side': 87, 'loss_of_smell': 88, 'bladder_discomfort': 89, 'foul_smell_of urine': 90, 'continuous_feel_of_urine': 91, 'passage_of_gases': 92, 'internal_itching': 93, 'toxic_look_(typhos)': 94, 'depression': 95, 'irritability': 96, 'muscle_pain': 97, 'altered_sensorium': 98, 'red_spots_over_body': 99, 'belly_pain': 100, 'abnormal_menstruation': 101, 'dischromic _patches': 102, 'watering_from_eyes': 103, 'increased_appetite': 104, 'polyuria': 105, 'family_history': 106, 'mucoid_sputum': 107, 'rusty_sputum': 108, 'lack_of_concentration': 109, 'visual_disturbances': 110, 'receiving_blood_transfusion': 111, 'receiving_unsterile_injections': 112, 'coma': 113, 'stomach_bleeding': 114, 'distention_of_abdomen': 115, 'history_of_alcohol_consumption': 116, 'fluid_overload.1': 117, 'blood_in_sputum': 118, 'prominent_veins_on_calf': 119, 'palpitations': 120, 'painful_walking': 121, 'pus_filled_pimples': 122, 'blackheads': 123, 'scurring': 124, 'skin_peeling': 125, 'silver_like_dusting': 126, 'small_dents_in_nails': 127, 'inflammatory_nails': 128, 'blister': 129, 'red_sore_around_nose': 130, 'yellow_crust_ooze': 131}\n", "diseases_list = {15: 'Fungal infection', 4: 'Allergy', 16: 'GERD', 9: 'Chronic cholestasis', 14: 'Drug Reaction', 33: 'Peptic ulcer diseae', 1: 'AIDS', 12: 'Diabetes ', 17: 'Gastroenteritis', 6: 'Bronchial Asthma', 23: 'Hypertension ', 30: 'Migraine', 7: 'Cervical spondylosis', 32: 'Paralysis (brain hemorrhage)', 28: 'Jaundice', 29: 'Malaria', 8: 'Chicken pox', 11: 'Dengue', 37: 'Typhoid', 40: 'hepatitis A', 19: 'Hepatitis B', 20: 'Hepatitis C', 21: 'Hepatitis D', 22: 'Hepatitis E', 3: 'Alcoholic hepatitis', 36: 'Tuberculosis', 10: 'Common Cold', 34: 'Pneumonia', 13: 'Dimorphic hemmorhoids(piles)', 18: 'Heart attack', 39: 'Varicose veins', 26: 'Hypothyroidism', 24: 'Hyperthyroidism', 25: 'Hypoglycemia', 31: 'Osteoarthristis', 5: 'Arthritis', 0: '(vertigo) Paroymsal  Positional Vertigo', 2: 'Acne', 38: 'Urinary tract infection', 35: 'Psoriasis', 27: 'Impetigo'}\n", "\n", "# Model Prediction function\n", "def get_predicted_value(patient_symptoms):\n", "    input_vector = np.zeros(len(symptoms_dict))\n", "    for item in patient_symptoms:\n", "        input_vector[symptoms_dict[item]] = 1\n", "    return diseases_list[svc.predict([input_vector])[0]]"]}, {"cell_type": "code", "execution_count": 121, "id": "a36b1e93", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Enter your symptoms.......itching,skin_rash,nodal_skin_eruptions\n", "=================predicted disease============\n", "Fungal infection\n", "=================description==================\n", "Fungal infection is a common skin condition caused by fungi.\n", "=================precautions==================\n", "1 :  bath twice\n", "2 :  use detol or neem in bathing water\n", "3 :  keep infected area dry\n", "4 :  use clean cloths\n", "=================medications==================\n", "5 :  ['Antifungal Cream', 'Fluconazole', 'Terbinafine', 'Clotrimazole', 'Ketoconazole']\n", "=================workout==================\n", "6 :  Avoid sugary foods\n", "7 :  Consume probiotics\n", "8 :  Increase intake of garlic\n", "9 :  Include yogurt in diet\n", "10 :  Limit processed foods\n", "11 :  Stay hydrated\n", "12 :  Consume green tea\n", "13 :  Eat foods rich in zinc\n", "14 :  Include turmeric in diet\n", "15 :  Eat fruits and vegetables\n", "=================diets==================\n", "16 :  ['Antifungal Diet', 'Probiotics', 'Garlic', 'Coconut oil', 'Turmeric']\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\sklearn\\base.py:465: UserWarning: X does not have valid feature names, but SVC was fitted with feature names\n", "  warnings.warn(\n"]}], "source": ["# Test 1\n", "# Split the user's input into a list of symptoms (assuming they are comma-separated) # itching,skin_rash,nodal_skin_eruptions\n", "symptoms = input(\"Enter your symptoms.......\")\n", "user_symptoms = [s.strip() for s in symptoms.split(',')]\n", "# Remove any extra characters, if any\n", "user_symptoms = [symptom.strip(\"[]' \") for symptom in user_symptoms]\n", "predicted_disease = get_predicted_value(user_symptoms)\n", "\n", "desc, pre, med, die, wrkout = helper(predicted_disease)\n", "\n", "print(\"=================predicted disease============\")\n", "print(predicted_disease)\n", "print(\"=================description==================\")\n", "print(desc)\n", "print(\"=================precautions==================\")\n", "i = 1\n", "for p_i in pre[0]:\n", "    print(i, \": \", p_i)\n", "    i += 1\n", "\n", "print(\"=================medications==================\")\n", "for m_i in med:\n", "    print(i, \": \", m_i)\n", "    i += 1\n", "\n", "print(\"=================workout==================\")\n", "for w_i in wrkout:\n", "    print(i, \": \", w_i)\n", "    i += 1\n", "\n", "print(\"=================diets==================\")\n", "for d_i in die:\n", "    print(i, \": \", d_i)\n", "    i += 1\n"]}, {"cell_type": "code", "execution_count": 122, "id": "2d7ee79b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Enter your symptoms.......yellow_crust_ooze,red_sore_around_nose,small_dents_in_nails,inflammatory_nails,blister\n", "=================predicted disease============\n", "Impetigo\n", "=================description==================\n", "Impetigo is a highly contagious skin infection causing red sores that can break open.\n", "=================precautions==================\n", "1 :  soak affected area in warm water\n", "2 :  use antibiotics\n", "3 :  remove scabs with wet compressed cloth\n", "4 :  consult doctor\n", "=================medications==================\n", "5 :  ['Topical antibiotics', 'Oral antibiotics', 'Antiseptics', 'Ointments', 'Warm compresses']\n", "=================workout==================\n", "6 :  Maintain good hygiene\n", "7 :  Stay hydrated\n", "8 :  Consume nutrient-rich foods\n", "9 :  Limit sugary foods and beverages\n", "10 :  Include foods rich in vitamin C\n", "11 :  Consult a healthcare professional\n", "12 :  Follow medical recommendations\n", "13 :  Avoid scratching\n", "14 :  Take prescribed antibiotics\n", "15 :  Practice wound care\n", "=================diets==================\n", "16 :  ['Impetigo Diet', 'Antibiotic treatment', 'Fruits and vegetables', 'Hydration', 'Protein-rich foods']\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\sklearn\\base.py:465: UserWarning: X does not have valid feature names, but SVC was fitted with feature names\n", "  warnings.warn(\n"]}], "source": ["# Test 1\n", "# Split the user's input into a list of symptoms (assuming they are comma-separated) # yellow_crust_ooze,red_sore_around_nose,small_dents_in_nails,inflammatory_nails,blister\n", "symptoms = input(\"Enter your symptoms.......\")\n", "user_symptoms = [s.strip() for s in symptoms.split(',')]\n", "# Remove any extra characters, if any\n", "user_symptoms = [symptom.strip(\"[]' \") for symptom in user_symptoms]\n", "predicted_disease = get_predicted_value(user_symptoms)\n", "\n", "desc, pre, med, die, wrkout = helper(predicted_disease)\n", "\n", "print(\"=================predicted disease============\")\n", "print(predicted_disease)\n", "print(\"=================description==================\")\n", "print(desc)\n", "print(\"=================precautions==================\")\n", "i = 1\n", "for p_i in pre[0]:\n", "    print(i, \": \", p_i)\n", "    i += 1\n", "\n", "print(\"=================medications==================\")\n", "for m_i in med:\n", "    print(i, \": \", m_i)\n", "    i += 1\n", "\n", "print(\"=================workout==================\")\n", "for w_i in wrkout:\n", "    print(i, \": \", w_i)\n", "    i += 1\n", "\n", "print(\"=================diets==================\")\n", "for d_i in die:\n", "    print(i, \": \", d_i)\n", "    i += 1\n"]}, {"cell_type": "code", "execution_count": 123, "id": "a8d5df35", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.3.2\n"]}], "source": ["# let's use pycharm flask app\n", "# but install this version in pycharm\n", "import sklearn\n", "print(sklearn.__version__)"]}, {"cell_type": "code", "execution_count": null, "id": "97dfb973", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}}, "nbformat": 4, "nbformat_minor": 5}