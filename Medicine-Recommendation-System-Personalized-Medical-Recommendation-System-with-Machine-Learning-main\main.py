from flask import Flask, request, render_template, jsonify  # Import jsonify
import numpy as np
import pandas as pd
import pickle


# flask app
app = Flask(__name__)



# load databasedataset===================================
sym_des = pd.read_csv("symtoms_df.csv")
precautions = pd.read_csv("precautions_df.csv")
workout = pd.read_csv("workout_df.csv")
description = pd.read_csv("description.csv")
medications = pd.read_csv('medications.csv')
diets = pd.read_csv("diets.csv")


# load model===========================================
svc = pickle.load(open('svc.pkl','rb'))


#============================================================
# custome and helping functions
#==========================helper funtions================
def helper(dis):
    desc = description[description['Disease'] == dis]['Description']
    desc = " ".join([w for w in desc])

    pre = precautions[precautions['Disease'] == dis][['Precaution_1', 'Precaution_2', 'Precaution_3', 'Precaution_4']]
    pre = [col for col in pre.values]

    med = medications[medications['Disease'] == dis]['Medication']
    med = [med for med in med.values]

    die = diets[diets['Disease'] == dis]['Diet']
    die = [die for die in die.values]

    wrkout = workout[workout['disease'] == dis] ['workout']


    return desc,pre,med,die,wrkout

symptoms_dict = {'itching': 0, 'skin_rash': 1, 'nodal_skin_eruptions': 2, 'continuous_sneezing': 3, 'shivering': 4, 'chills': 5, 'joint_pain': 6, 'stomach_pain': 7, 'acidity': 8, 'ulcers_on_tongue': 9, 'muscle_wasting': 10, 'vomiting': 11, 'burning_micturition': 12, 'spotting_ urination': 13, 'fatigue': 14, 'weight_gain': 15, 'anxiety': 16, 'cold_hands_and_feets': 17, 'mood_swings': 18, 'weight_loss': 19, 'restlessness': 20, 'lethargy': 21, 'patches_in_throat': 22, 'irregular_sugar_level': 23, 'cough': 24, 'high_fever': 25, 'sunken_eyes': 26, 'breathlessness': 27, 'sweating': 28, 'dehydration': 29, 'indigestion': 30, 'headache': 31, 'yellowish_skin': 32, 'dark_urine': 33, 'nausea': 34, 'loss_of_appetite': 35, 'pain_behind_the_eyes': 36, 'back_pain': 37, 'constipation': 38, 'abdominal_pain': 39, 'diarrhoea': 40, 'mild_fever': 41, 'yellow_urine': 42, 'yellowing_of_eyes': 43, 'acute_liver_failure': 44, 'fluid_overload': 45, 'swelling_of_stomach': 46, 'swelled_lymph_nodes': 47, 'malaise': 48, 'blurred_and_distorted_vision': 49, 'phlegm': 50, 'throat_irritation': 51, 'redness_of_eyes': 52, 'sinus_pressure': 53, 'runny_nose': 54, 'congestion': 55, 'chest_pain': 56, 'weakness_in_limbs': 57, 'fast_heart_rate': 58, 'pain_during_bowel_movements': 59, 'pain_in_anal_region': 60, 'bloody_stool': 61, 'irritation_in_anus': 62, 'neck_pain': 63, 'dizziness': 64, 'cramps': 65, 'bruising': 66, 'obesity': 67, 'swollen_legs': 68, 'swollen_blood_vessels': 69, 'puffy_face_and_eyes': 70, 'enlarged_thyroid': 71, 'brittle_nails': 72, 'swollen_extremeties': 73, 'excessive_hunger': 74, 'extra_marital_contacts': 75, 'drying_and_tingling_lips': 76, 'slurred_speech': 77, 'knee_pain': 78, 'hip_joint_pain': 79, 'muscle_weakness': 80, 'stiff_neck': 81, 'swelling_joints': 82, 'movement_stiffness': 83, 'spinning_movements': 84, 'loss_of_balance': 85, 'unsteadiness': 86, 'weakness_of_one_body_side': 87, 'loss_of_smell': 88, 'bladder_discomfort': 89, 'foul_smell_of urine': 90, 'continuous_feel_of_urine': 91, 'passage_of_gases': 92, 'internal_itching': 93, 'toxic_look_(typhos)': 94, 'depression': 95, 'irritability': 96, 'muscle_pain': 97, 'altered_sensorium': 98, 'red_spots_over_body': 99, 'belly_pain': 100, 'abnormal_menstruation': 101, 'dischromic _patches': 102, 'watering_from_eyes': 103, 'increased_appetite': 104, 'polyuria': 105, 'family_history': 106, 'mucoid_sputum': 107, 'rusty_sputum': 108, 'lack_of_concentration': 109, 'visual_disturbances': 110, 'receiving_blood_transfusion': 111, 'receiving_unsterile_injections': 112, 'coma': 113, 'stomach_bleeding': 114, 'distention_of_abdomen': 115, 'history_of_alcohol_consumption': 116, 'fluid_overload.1': 117, 'blood_in_sputum': 118, 'prominent_veins_on_calf': 119, 'palpitations': 120, 'painful_walking': 121, 'pus_filled_pimples': 122, 'blackheads': 123, 'scurring': 124, 'skin_peeling': 125, 'silver_like_dusting': 126, 'small_dents_in_nails': 127, 'inflammatory_nails': 128, 'blister': 129, 'red_sore_around_nose': 130, 'yellow_crust_ooze': 131}
diseases_list = {15: 'Fungal infection', 4: 'Allergy', 16: 'GERD', 9: 'Chronic cholestasis', 14: 'Drug Reaction', 33: 'Peptic ulcer diseae', 1: 'AIDS', 12: 'Diabetes ', 17: 'Gastroenteritis', 6: 'Bronchial Asthma', 23: 'Hypertension ', 30: 'Migraine', 7: 'Cervical spondylosis', 32: 'Paralysis (brain hemorrhage)', 28: 'Jaundice', 29: 'Malaria', 8: 'Chicken pox', 11: 'Dengue', 37: 'Typhoid', 40: 'hepatitis A', 19: 'Hepatitis B', 20: 'Hepatitis C', 21: 'Hepatitis D', 22: 'Hepatitis E', 3: 'Alcoholic hepatitis', 36: 'Tuberculosis', 10: 'Common Cold', 34: 'Pneumonia', 13: 'Dimorphic hemmorhoids(piles)', 18: 'Heart attack', 39: 'Varicose veins', 26: 'Hypothyroidism', 24: 'Hyperthyroidism', 25: 'Hypoglycemia', 31: 'Osteoarthristis', 5: 'Arthritis', 0: '(vertigo) Paroymsal  Positional Vertigo', 2: 'Acne', 38: 'Urinary tract infection', 35: 'Psoriasis', 27: 'Impetigo'}

# Disease severity mapping (1=mild, 2=moderate, 3=severe)
disease_severity = {
    'Common Cold': 1,
    'Allergy': 1,
    'Migraine': 2,
    'GERD': 2,
    'Acne': 1,
    'Fungal infection': 1,
    'Urinary tract infection': 2,
    'Gastroenteritis': 2,
    'Bronchial Asthma': 2,
    'Hypertension': 2,
    'Diabetes': 3,
    'Hypothyroidism': 2,
    'Hyperthyroidism': 2,
    'Osteoarthristis': 2,
    'Arthritis': 2,
    'Psoriasis': 2,
    'Impetigo': 1,
    'Drug Reaction': 2,
    'Peptic ulcer diseae': 2,
    'Chronic cholestasis': 3,
    'Hypoglycemia': 2,
    'Varicose veins': 2,
    'Dimorphic hemmorhoids(piles)': 2,
    'Cervical spondylosis': 2,
    'Chicken pox': 2,
    'Jaundice': 3,
    'Malaria': 3,
    'Dengue': 3,
    'Typhoid': 3,
    'hepatitis A': 3,
    'Hepatitis B': 3,
    'Hepatitis C': 3,
    'Hepatitis D': 3,
    'Hepatitis E': 3,
    'Alcoholic hepatitis': 3,
    'Tuberculosis': 3,
    'Pneumonia': 3,
    'Heart attack': 3,
    'Paralysis (brain hemorrhage)': 3,
    '(vertigo) Paroymsal Positional Vertigo': 2,
    'AIDS': 3
}

# Common symptom to mild disease mapping (including common user inputs)
mild_disease_mapping = {
    'headache': ['Migraine', 'Common Cold', 'Allergy'],
    'cough': ['Common Cold', 'Allergy', 'Bronchial Asthma'],
    'fever': ['Common Cold', 'Allergy'],  # User input
    'high_fever': ['Common Cold', 'Allergy'],  # Actual symptom
    'mild_fever': ['Common Cold', 'Allergy'],
    'fatigue': ['Common Cold', 'Allergy'],
    'weakness': ['Common Cold', 'Allergy'],  # User input
    'cold': ['Common Cold', 'Allergy'],  # User input
    'nausea': ['Gastroenteritis', 'Migraine'],
    'vomiting': ['Gastroenteritis', 'Migraine'],
    'stomach_pain': ['Gastroenteritis', 'Peptic ulcer diseae'],
    'back_pain': ['Cervical spondylosis', 'Osteoarthristis'],
    'joint_pain': ['Arthritis', 'Osteoarthristis'],
    'skin_rash': ['Allergy', 'Fungal infection'],
    'itching': ['Allergy', 'Fungal infection'],
    'runny_nose': ['Common Cold', 'Allergy'],
    'sneezing': ['Common Cold', 'Allergy'],
    'continuous_sneezing': ['Common Cold', 'Allergy']
}

# Symptom mapping for common user inputs to actual symptoms in the dataset
symptom_mapping = {
    'fever': 'high_fever',
    'cold': 'runny_nose',
    'weakness': 'fatigue',
    'tired': 'fatigue',
    'pain': 'headache',
    'ache': 'headache',
    'sneezing': 'continuous_sneezing',
    'rash': 'skin_rash'
}

def get_predicted_value(patient_symptoms):
    print(f"DEBUG: get_predicted_value called with: {patient_symptoms}")

    # Check if input is empty
    if not patient_symptoms:
        print(f"DEBUG: Empty patient_symptoms, returning None")
        return None  # Return None instead of a default disease

    # Normalize and map symptoms
    normalized_symptoms = []
    for symptom in patient_symptoms:
        # Map common user inputs to actual symptoms
        if symptom in symptom_mapping:
            mapped = symptom_mapping[symptom]
            print(f"DEBUG: Mapped '{symptom}' to '{mapped}'")
            normalized_symptoms.append(mapped)
        else:
            print(f"DEBUG: No mapping for '{symptom}', keeping as is")
            normalized_symptoms.append(symptom)

    print(f"DEBUG: Normalized symptoms: {normalized_symptoms}")

    # Filter out symptoms that don't exist in the dataset
    valid_symptoms = [s for s in normalized_symptoms if s in symptoms_dict]

    print(f"DEBUG: Valid symptoms in dataset: {valid_symptoms}")

    # If no valid symptoms found, return None instead of a default
    if not valid_symptoms:
        print(f"DEBUG: No valid symptoms found in dataset, returning None")
        return None

    # If only one symptom and it's a common symptom, suggest mild diseases first
    if len(valid_symptoms) == 1:
        symptom = valid_symptoms[0]
        # Check original symptom too for mapping
        original_symptom = patient_symptoms[0] if len(patient_symptoms) == 1 else symptom
        if symptom in mild_disease_mapping or original_symptom in mild_disease_mapping:
            mapping_key = original_symptom if original_symptom in mild_disease_mapping else symptom
            return mild_disease_mapping[mapping_key][0]  # Return the most common mild disease

    # If 2 or fewer symptoms, avoid severe diseases
    if len(valid_symptoms) <= 2:
        input_vector = np.zeros(len(symptoms_dict))
        for item in valid_symptoms:
            input_vector[symptoms_dict[item]] = 1

        # Get the single prediction from the model
        predicted_disease = diseases_list[svc.predict([input_vector])[0]]

        # Filter out severe diseases for simple symptoms
        if disease_severity.get(predicted_disease, 2) <= 2:  # Only mild to moderate diseases
            return predicted_disease

        # If prediction is severe, return the most common mild disease for the symptoms
        for symptom in valid_symptoms:
            if symptom in mild_disease_mapping:
                return mild_disease_mapping[symptom][0]

        return 'Common Cold'  # Default safe prediction

    # For 3+ symptoms, use normal prediction but still be cautious
    input_vector = np.zeros(len(symptoms_dict))
    for item in valid_symptoms:
        input_vector[symptoms_dict[item]] = 1

    predicted_disease = diseases_list[svc.predict([input_vector])[0]]

    # Additional safety check: if predicted disease is very severe and symptoms are common,
    # suggest a moderate alternative
    severe_diseases = ['Paralysis (brain hemorrhage)', 'Heart attack', 'AIDS', 'Tuberculosis']
    if predicted_disease in severe_diseases:
        common_symptoms = ['headache', 'high_fever', 'cough', 'fatigue', 'nausea']
        if any(symptom in common_symptoms for symptom in valid_symptoms):
            # Return a safer alternative for common symptoms
            for symptom in valid_symptoms:
                if symptom in mild_disease_mapping:
                    return mild_disease_mapping[symptom][0]
            return 'Common Cold'  # Safe fallback

    return predicted_disease




# creating routes========================================


@app.route("/")
def index():
    return render_template("index.html")

# Define a route for the home page
@app.route('/predict', methods=['GET', 'POST'])
def home():
    if request.method == 'POST':
        symptoms = request.form.get('symptoms')
        # mysysms = request.form.get('mysysms')
        # print(mysysms)
        print(symptoms)

        # Check for empty or invalid input
        if not symptoms or symptoms.strip() == "" or symptoms == "Symptoms":
            message = "⚠️ Please enter at least one symptom to get a prediction. Example: headache, fever, cough"
            return render_template('index.html', message=message)

        # Split the user's input into a list of symptoms (assuming they are comma-separated)
        user_symptoms = [s.strip() for s in symptoms.split(',')]
        # Remove any extra characters, if any
        user_symptoms = [symptom.strip("[]' ") for symptom in user_symptoms]

        print(f"DEBUG: Raw symptoms: {symptoms}")
        print(f"DEBUG: Processed symptoms: {user_symptoms}")

        # Filter out empty symptoms after processing
        user_symptoms = [symptom for symptom in user_symptoms if symptom and symptom.strip()]

        print(f"DEBUG: Filtered symptoms: {user_symptoms}")

        # Check if we have any valid symptoms after cleaning
        if not user_symptoms:
            message = "⚠️ No valid symptoms detected. Please enter symptoms separated by commas. Example: headache, fever, cough"
            print(f"DEBUG: No symptoms after filtering")
            return render_template('index.html', message=message)

        predicted_disease = get_predicted_value(user_symptoms)
        print(f"DEBUG: Predicted disease: {predicted_disease}")

        # Handle case where no valid symptoms were found in the dataset
        if predicted_disease is None:
            message = "⚠️ The symptoms you entered are not recognized by our system. Please check spelling or try common symptoms like: headache, fever, cough, nausea, fatigue"
            print(f"DEBUG: No valid symptoms found for: {user_symptoms}")
            return render_template('index.html', message=message)
            dis_des, precautions, medications, rec_diet, workout = helper(predicted_disease)

            my_precautions = []
            for i in precautions[0]:
                my_precautions.append(i)

            # Add medical disclaimer and severity warning
            medical_disclaimer = """
            ⚠️ IMPORTANT MEDICAL DISCLAIMER ⚠️
            This is an AI-based prediction system for educational purposes only.
            It should NOT replace professional medical consultation.
            Please consult a qualified healthcare provider for proper diagnosis and treatment.
            """

            # Determine if this is a serious condition that needs immediate attention
            serious_conditions = ['Paralysis (brain hemorrhage)', 'Heart attack', 'AIDS', 'Tuberculosis',
                                'hepatitis A', 'Hepatitis B', 'Hepatitis C', 'Hepatitis D', 'Hepatitis E',
                                'Dengue', 'Malaria', 'Typhoid', 'Pneumonia']

            urgency_message = ""
            if predicted_disease in serious_conditions:
                urgency_message = "🚨 This condition may require immediate medical attention. Please consult a healthcare provider immediately."
            elif len(user_symptoms) == 1:
                urgency_message = "💡 Single symptoms can have many causes. Consider monitoring for additional symptoms and consult a doctor if symptoms persist or worsen."
            else:
                urgency_message = "📋 Based on your symptoms, this is a preliminary assessment. Please consult a healthcare provider for proper diagnosis."

            return render_template('index.html', predicted_disease=predicted_disease, dis_des=dis_des,
                                   my_precautions=my_precautions, medications=medications, my_diet=rec_diet,
                                   workout=workout, medical_disclaimer=medical_disclaimer,
                                   urgency_message=urgency_message, symptom_count=len(user_symptoms))

    return render_template('index.html')



# about view funtion and path
@app.route('/about')
def about():
    return render_template("about.html")
# contact view funtion and path
@app.route('/contact')
def contact():
    return render_template("contact.html")

# developer view funtion and path
@app.route('/developer')
def developer():
    return render_template("developer.html")

# about view funtion and path
@app.route('/blog')
def blog():
    return render_template("blog.html")


if __name__ == '__main__':

    app.run(debug=True)